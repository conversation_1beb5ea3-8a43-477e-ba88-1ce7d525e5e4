import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { ContractManager } from '@/types/contracts/ContractManager'

wrappedTask('setIBCApp', 'register ibc app address to contract manager', {
  filePath: path.basename(__filename),
})
  .addParam('ibcapp', 'ibc app contract address')
  .addParam('ibcappName', 'ibc app name choose "JPYTokenTransferBridge" or "BalanceSyncBridge" or "AccountSyncBridge"')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const { ibcapp = '', ibcappName = '' } = taskArguments

    const { contract } = await getContractWithSigner<ContractManager>({ hre, contractName: 'ContractManager' })

    if (
      ibcappName === 'JPYTokenTransferBridge' ||
      ibcappName === 'BalanceSyncBridge' ||
      ibcappName === 'AccountSyncBridge'
    ) {
      console.log(`*** IBC/APP登録(${ibcappName}): ${ibcapp.toString(16)}`)

      const deadline = await getTime()
      // ibcappName = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes(ibcappName)).padEnd(66, '0');

      const kmsSig = await kmsSigner.sign(['address', 'uint256'], [ibcapp, deadline])

      await executeReceipt(contract.setIbcApp(ibcapp, ibcappName, deadline, kmsSig))
    } else {
      console.error(
        `Unknown IBC App Name: ${ibcappName}, choose 'JPYTokenTransferBridge' or 'BalanceSyncBridge' or 'AccountSyncBridge'`,
      )
    }
  })
