diff --git a/UT_before.txt b/UT_after.txt
index b2aa0ad5..3fa7bee7 100644
--- a/UT_before.txt
+++ b/UT_after.txt
@@ -45,6 +45,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         ✔ versionが取得できること
     addAdminRole()
       正常系
@@ -112,6 +127,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Providerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限が付与されること
         accounts[5]に権限が付与されている状態
@@ -158,6 +188,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Providerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
           ✔ 呼び出し元がProviderではない場合、エラーがスローされること
@@ -205,6 +250,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限が付与されること
         accounts[5]に権限が付与されている状態
@@ -251,6 +311,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
           ✔ 呼び出し元がIssuerではない場合、エラーがスローされること
@@ -298,6 +373,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限が付与されること
         accounts[5]に権限が付与されている状態
@@ -344,6 +434,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         Validatorのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態
           ✔ 権限にAdmin権限を指定した場合、エラーがスローされること
           ✔ 呼び出し元がValidatorではない場合、エラーがスローされること
@@ -408,6 +513,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにProvider権限が付与されている状態
           ✔ 指定した権限がある場合、trueが返されること
           ✔ Provider権限でない署名の場合、falseが返されること
@@ -465,6 +585,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         ✔ 署名期限切れの場合、GS0003エラーが返されること
         ✔ validatorIdのアドレスが登録されている場合、正常終了すること
         ✔ validatorIdのアドレスが一致しない場合、GS0002エラーが返されること
@@ -520,6 +655,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにProvider権限が付与されている状態
           ✔ Provider権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
           ✔ 権限が削除されること
@@ -565,6 +715,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにProvider権限が付与されている状態
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
     delIssuerRole()
@@ -610,6 +775,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにIssuer権限が付与されている状態
           ✔ Issuer権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
           ✔ 権限が削除されること
@@ -655,6 +835,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにIssuer権限が付与されている状態
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
     delValidatorRole()
@@ -700,6 +895,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにValidator権限が付与されている状態
           ✔ Validator権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと
           ✔ 権限が削除されること
@@ -745,6 +955,21 @@ npx hardhat test
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"}],"name":"getValidatorIdsCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IValidatorStorage","name":"validatorStorage","type":"IValidatorStorage"},{"internalType":"uint256","name":"limit","type":"uint256"},{"internalType":"uint256","name":"offset","type":"uint256"}],"name":"getValidatorList","outputs":[{"components":[{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"}],"internalType":"struct ValidatorListData[]","name":"validators","type":"tuple[]"},{"internalType":"uint256","name":"totalCount","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IValidatorStorage", code=INVALID_ARGUMENT, version=6.13.4)
 [Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint16","name":"zoneId","type":"uint16"}],"name":"getZonesForFinancialAccount","outputs":[{"components":[{"internalType":"uint16","name":"zoneId","type":"uint16"},{"internalType":"string","name":"zoneName","type":"string"},{"internalType":"bytes32[]","name":"availableIssuerIds","type":"bytes32[]"}],"internalType":"struct ZoneData[]","name":"zones","type":"tuple[]"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"addTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"issuerId","type":"bytes32"},{"internalType":"bytes32","name":"accountId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"blockTimestamp","type":"uint256"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"internalType":"struct BurnCancelData","name":"burnCancelData","type":"tuple"}],"name":"burnCancelIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"checkAccountTermination","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"bytes","name":"accountSignature","type":"bytes"},{"internalType":"bytes","name":"info","type":"bytes"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"checkApprove","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"},{"internalType":"bytes32","name":"ownerId","type":"bytes32"},{"internalType":"bytes32","name":"spenderId","type":"bytes32"}],"name":"getAllowance","outputs":[{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"approvedAt","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"accountId","type":"bytes32"}],"name":"getBalanceList","outputs":[{"internalType":"uint16[]","name":"zoneIds","type":"uint16[]"},{"internalType":"string[]","name":"zoneNames","type":"string[]"},{"internalType":"uint256[]","name":"balances","type":"uint256[]"},{"internalType":"string[]","name":"accountNames","type":"string[]"},{"internalType":"bytes32[]","name":"accountStatus","type":"bytes32[]"},{"internalType":"uint256","name":"totalBalance","type":"uint256"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getToken","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenAll","outputs":[{"components":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"name","type":"bytes32"},{"internalType":"bytes32","name":"symbol","type":"bytes32"},{"internalType":"uint256","name":"totalSupply","type":"uint256"},{"internalType":"bool","name":"enabled","type":"bool"}],"internalType":"struct TokenAll","name":"token","type":"tuple"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"}],"name":"getTokenId","outputs":[{"internalType":"bytes32","name":"tokenId","type":"bytes32"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"getTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bytes32","name":"chkTokenId","type":"bytes32"},{"internalType":"bool","name":"chkEnabled","type":"bool"}],"name":"hasToken","outputs":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"string","name":"err","type":"string"}],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="ITokenStorage", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"bytes32","name":"validatorId","type":"bytes32"}],"name":"hasValidator","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"address","name":"sender","type":"address"}],"name":"modTokenIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
+[Warning] Invalid Fragment {"inputs":[{"internalType":"contract IContractManager","name":"contractManager","type":"IContractManager"},{"internalType":"contract ITokenStorage","name":"tokenStorage","type":"ITokenStorage"},{"internalType":"bytes32","name":"providerId","type":"bytes32"},{"internalType":"bytes32","name":"tokenId","type":"bytes32"},{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"deadline","type":"uint256"},{"internalType":"bytes","name":"signature","type":"bytes"}],"name":"setTokenEnabledIsValid","outputs":[],"stateMutability":"view","type":"function"}: invalid type (argument="type", value="IContractManager", code=INVALID_ARGUMENT, version=6.13.4)
         アカウントにValidator権限が付与されている状態
           ✔ 呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること
 
