import fs from 'fs'
import path from 'path'
import readline from 'readline'
import chalk from 'chalk'
import inquirer from 'inquirer'

export const ROOTDIR = getRootDir()
export const SCRIPTDIR = path.join(ROOTDIR, 'bin', 'main')
export const ENV_FILE_PATH = path.join(ROOTDIR, '.env')

export const IBCAPP_TOKEN_TRANSFER = 'JPYTokenTransferBridge'
export const IBCAPP_ACCOUNT_SYNC = 'AccountSyncBridge'
export const IBCAPP_BALANCE_SYNC = 'BalanceSyncBridge'

export const SRC_ZONE_ID = '3000'
export const ESCROW_ACCOUNT = '61000e3ac0011d520cf4f8f8c33c4a71'

/**
 * gitリポジトリのルートディレクトリを取得
 * @returns ルートディレクトリのパス
 */
function getRootDir() {
  let dir = path.resolve(__dirname)

  // ルートディレクトリに到達するまで上にたどって .git を探す
  while (dir !== path.parse(dir).root) {
    if (fs.existsSync(path.join(dir, '.git'))) {
      break
    }
    dir = path.dirname(dir)
  }

  return dir
}

/**
 * 全角・半角を判定して幅を計算する
 * @param str 文字列
 * @returns 幅
 */
function getDisplayWidth(str: string): number {
  let width = 0
  for (const char of str) {
    const code = char.codePointAt(0)
    if (code === undefined) {
      throw new Error('Invalid character: code point is undefined')
    }
    if (
      (code >= 0x1100 && code <= 0x115f) || // Hangul Jamo
      (code >= 0x2e80 && code <= 0xa4cf) || // CJK
      (code >= 0xac00 && code <= 0xd7a3) || // Hangul
      (code >= 0xf900 && code <= 0xfaff) || // CJK Compatibility Ideographs
      (code >= 0xfe10 && code <= 0xfe19) || // Vertical forms
      (code >= 0xfe30 && code <= 0xfe6f) || // CJK Compatibility Forms
      (code >= 0xff00 && code <= 0xff60) || // Fullwidth forms
      (code >= 0xffe0 && code <= 0xffe6)
    ) {
      width += 2
    } else {
      width += 1
    }
  }
  return width
}

/**
 * 表示幅に合わせて右側にスペースを足してパディングした文字列を返す
 * @param str 文字列
 * @param targetWidth 表示幅
 * @returns 右側にスペースを足した文字列
 */
function padDisplayWidth(str: string, targetWidth: number): string {
  const currentWidth = getDisplayWidth(str)
  const padding = Math.max(0, targetWidth - currentWidth)
  return str + ' '.repeat(padding)
}

/**
 * 複数行のタイトル文字列をダブルラインの枠で囲んで整形・装飾して表示する
 * @param title タイトル文字列
 */
export function printDoubleLineTitle(title: string): void {
  // 引数の改行を分割して配列化
  const lines = title.split(/\r?\n/)

  // 最長行の長さを取得
  const maxDisplayWidth = Math.max(...lines.map(getDisplayWidth))
  const totalWidth = maxDisplayWidth + 4 // 左右に2スペース分

  // 枠線文字
  const topLeft = '╔'
  const topRight = '╗'
  const bottomLeft = '╚'
  const bottomRight = '╝'
  const horizontal = '═'
  const vertical = '║'

  // 上の枠線
  console.log()
  console.log(chalk.blue(topLeft + horizontal.repeat(totalWidth) + topRight))

  // 中身の行を1行ずつ出力（太字＋青枠）
  for (const line of lines) {
    // 左右にスペース1つずつ入れて中央寄せ
    const paddedLine = padDisplayWidth(line, maxDisplayWidth)
    console.log(chalk.blue(vertical) + '  ' + chalk.bold(paddedLine) + '  ' + chalk.blue(vertical))
  }

  // 下の枠線
  console.log(chalk.blue(bottomLeft + horizontal.repeat(totalWidth) + bottomRight))
  console.log()
}

type MessageType = 'i' | 'info' | 'w' | 'warn' | 'e' | 'err' | 's' | 'success' | 'q' | 'question'

/**
 * 指定したメッセージタイプに応じた絵文字付きのカラー出力を行う
 * @param type i(info),w(warn),e(err),s(success),q(question)
 * @param text 文字列
 */
export function message(type: unknown, text: string, newline = true): void {
  const validTypes: MessageType[] = ['i', 'info', 'w', 'warn', 'e', 'err', 's', 'success', 'q', 'question']

  if (typeof type !== 'string' || !validTypes.includes(type as MessageType)) {
    return
  }

  const t = type.toLowerCase()
  const iconColorMap: Record<string, { icon: string; color: chalk.Chalk }> = {
    i: { icon: '✔️', color: chalk.blue.bold },
    w: { icon: '🚧', color: chalk.yellow.bold },
    e: { icon: '🔥', color: chalk.red.bold },
    s: { icon: '✅', color: chalk.green.bold },
    q: { icon: '💭', color: chalk.magenta.bold },
  }

  const { icon, color } = iconColorMap[t[0]]
  const output = `${icon} ${color(text)}`
  if (newline) {
    console.log(output)
  } else {
    process.stdout.write(output)
  }
}

/**
 * 対話式のキーボード操作ができるCLIメニューUIを表示する
 * @param title タイトル文字列
 * @param items 選択肢の配列
 * @returns
 */
export async function menu(title: string, items: string[]): Promise<string> {
  const answers = await inquirer.prompt<{ choice: string }>([
    {
      type: 'list',
      name: 'choice',
      message: title,
      choices: items,
      loop: false,
    },
  ])
  const choice = answers.choice

  // ユーザー選択後選択肢を再表示
  for (const item of items) {
    if (item === choice) {
      console.log(chalk.green(`✔ ${item}`))
    } else {
      console.log(`  ${item}`)
    }
  }

  return choice
}

/**
 * ユーザーにYes/Noの選択を促し、ユーザーの入力がyまたはYでない限り処理を中断する
 * @param promptText 文字列
 */
export async function choice(promptText: string): Promise<void> {
  return new Promise((resolve) => {
    // 質問を message 関数で装飾して出力
    message('i', `${promptText} (y/N): `, false)

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    })

    rl.on('line', (input) => {
      rl.close()

      if (input.trim().toLowerCase() !== 'y') {
        message('e', 'canceled.')
        process.exit(1)
      }

      resolve()
    })
  })
}
