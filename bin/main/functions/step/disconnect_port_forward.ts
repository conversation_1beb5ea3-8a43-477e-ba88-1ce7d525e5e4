import { execSync, spawnSync } from 'child_process'
import path from 'path'
import { setTimeout as sleep } from 'timers/promises'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * ポートフォワードを切断する
 * @param network 環境名
 */
export async function disconnectPortForward(network: string) {
  try {
    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    // ローカル環境向けのリリースの場合、処理をスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      process.exit(0)
    }

    // PROVIDER 確認とポート抽出
    const provider = process.env.PROVIDER
    if (!provider) {
      message('err', 'PROVIDER environment variable is not set.')
      process.exit(1)
    }
    const providerParts = provider.split(':')
    const providerPort = providerParts[2]
    if (!providerPort) {
      message('err', 'Failed to extract port number from PROVIDER environment variable.')
      process.exit(1)
    }

    // ポートフォワード切断
    const pid = getPidByPort(providerPort)
    if (pid) {
      message('info', `Terminating process ${pid} on port ${providerPort}`)
      await terminateProcess(pid)
    } else {
      message('info', `No process found running on port ${providerPort}.`)
      process.exit(0)
    }
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

/**
 * 指定したポート番号から実行中のプロセスIDを取得する
 * @param port ポート番号
 * @returns プロセスID
 */
function getPidByPort(port: string): string | null {
  try {
    const result = execSync(`lsof -t -i:${port}`, { encoding: 'utf-8' }).trim()
    return result || null
  } catch {
    return null
  }
}

/**
 * 指定したプロセスが実行中かどうか判定する
 * @param pid プロセスID
 * @returns true:実行中、false:実行されていない
 */
function isProcessRunning(pid: string): boolean {
  const result = spawnSync('kill', ['-0', pid])
  return result.status === 0
}

/**
 * 指定したプロセスを終了させる
 *
 * 最初に SIGTERM を送信し、最大10秒間待機してプロセスの終了を確認する
 * 終了しない場合は SIGKILL を送信し、強制的にプロセスを終了させる
 *
 * @param pid プロセスID
 */
async function terminateProcess(pid: string) {
  try {
    process.kill(Number(pid))
  } catch (err) {
    message('err', `Failed to send SIGTERM to process ${pid}: ${err}`)
    process.exit(1)
  }
  for (let i = 0; i < 10; i++) {
    if (!isProcessRunning(pid)) {
      message('success', `Process ${pid} terminated successfully.`)
      process.exit(0)
    }
    await sleep(1000)
  }

  message('warn', `Process ${pid} did not terminate, killing it forcefully.`)
  try {
    process.kill(Number(pid), 'SIGKILL')
  } catch (err) {
    message('err', `Failed to send SIGKILL to process ${pid}: ${err}`)
    process.exit(1)
  }
  if (!isProcessRunning(pid)) {
    message('success', `Process ${pid} killed forcefully.`)
    process.exit(0)
  } else {
    message('err', `Failed to kill process ${pid}.`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  disconnectPortForward(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
