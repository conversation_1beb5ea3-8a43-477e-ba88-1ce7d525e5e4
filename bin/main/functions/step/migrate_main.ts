import { execSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * mainコントラクトのデプロイを行う
 * @param network 環境名
 */
export async function migrateMain(network: string) {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    message('info', `Starting deployment for network: ${network}`)

    // ZONE_ID に応じてデプロイコマンドを作成
    const zoneId = process.env.ZONE_ID
    let DEPLOY_CMD: string

    if (!zoneId) {
      message('err', 'ZONE_ID environment variable is not set.')
      process.exit(1)
    }

    if (zoneId === '3000') {
      DEPLOY_CMD = `npx hardhat deploy --network ${network} --tags main-contracts`
    } else if (/^3\d{3}$/.test(zoneId) && zoneId !== '3000') {
      DEPLOY_CMD = `npx hardhat deploy --network ${network} --tags renewable`
    } else {
      message('err', `Invalid ZONE_ID: ${zoneId}. Please set ZONE_ID to 3000 ~ 3999.`)
      process.exit(1)
    }

    execSync(DEPLOY_CMD, { stdio: 'inherit' })
    message('success', `Deployment for network: ${network} completed`)
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  migrateMain(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
