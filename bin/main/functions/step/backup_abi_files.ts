import { spawnSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * ABIファイルのバックアップを行う
 * @param network 環境名
 * @param zoneId ゾーンID
 */
export async function backUpABIfiles(network: string, zoneId?: string) {
  try {
    // ローカル環境向けのリリースの場合、処理をスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      process.exit(0)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile, zoneId)

    const targetZoneId = process.env.ZONE_ID
    if (!targetZoneId) {
      message('err', 'ZONE_ID environment variable is not set.')
      process.exit(1)
    }

    // バックアップ先設定
    const backupBucket = process.env.BACKUP
    if (!backupBucket) {
      message('err', 'BACKUP environment variable is not set.')
      process.exit(1)
    }

    const now = getTimestampJST()
    const backupPath = `${backupBucket}/${targetZoneId}/contract/${network}/${now}`

    // S3 にアップロード
    message('info', `Backup hardhat deployments to s3://${backupPath}/`)
    const files = ['deployments', 'hardhat.config.ts', '.env', '.env_main_bk', '.env_ibc_bk', '.kms']

    for (const f of files) {
      if (fs.existsSync(f)) {
        const stat = fs.statSync(f)
        if (stat.isDirectory()) {
          spawnSync('aws', ['s3', 'cp', f, `s3://${backupPath}/${f}`, '--recursive'], { stdio: 'inherit' })
        } else {
          spawnSync('aws', ['s3', 'cp', f, `s3://${backupPath}/${f}`], { stdio: 'inherit' })
        }
      }
    }
    message('success', `Files have been successfully copied to s3://${backupPath}/`)

    // S3 上の確認
    spawnSync('aws', ['s3', 'ls', `s3://${backupPath}/`], { stdio: 'inherit' })
    message('success', 'Verification of the backed up files is complete.')
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

/**
 * 現在時刻(JST)の文字列を "YYYYMMDD-HHMMSS" 形式で生成
 * @returns 現在時刻の文字列
 */
function getTimestampJST(): string {
  const date = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Tokyo' }))
  const pad = (n: number) => n.toString().padStart(2, '0')

  const yyyy = date.getFullYear()
  const mm = pad(date.getMonth() + 1)
  const dd = pad(date.getDate())
  const hh = pad(date.getHours())
  const min = pad(date.getMinutes())
  const ss = pad(date.getSeconds())

  return `${yyyy}${mm}${dd}-${hh}${min}${ss}`
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const zoneId = process.argv[3]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name] [ZONE_ID ※任意]`)
    process.exit(1)
  }

  backUpABIfiles(network, zoneId).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
