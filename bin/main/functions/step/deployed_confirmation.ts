import { execSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * デプロイ確認を行う
 * @param network ネットワーク名
 * @param contractType コントラクト種別
 */
export async function deployedConfirmation(network: string, contractType: string) {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    // hardhatタスク実行
    let command
    if (contractType === 'main') {
      command = `npx hardhat deployConfirmation_main --network "${network}"`
    } else {
      command = `npx hardhat deployConfirmation_ibc --network "${network}"`
    }

    execSync(command, { stdio: 'inherit' })
  } catch (err) {
    message('err', `Failed to execute Hardhat command: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const contranctType = process.argv[3]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name] [CONTRACT type]`)
    process.exit(1)
  }

  deployedConfirmation(network, contranctType).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
